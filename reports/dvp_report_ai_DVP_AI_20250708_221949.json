{"id": "DVP_AI_20250708_221949", "generatedDate": "2025-07-08T22:19:49.966489", "config": {"configType": "ADAS L2级配置", "startDate": "2025-07-08", "sopDate": "2026-06-30", "components": ["AD摄像头前视/后视/侧视", "DPB+ESP/IPB", "EPS", "毫米波雷达"], "complexity": "standard", "environment": "normal", "priority": "normal", "testProjectsData": [{"name": "超声波雷达标定匹配", "optionalComponents": [], "requiredComponents": ["超声波雷达"], "testCycle": 3, "totalComponents": 1}, {"name": "毫米波雷达标定匹配", "optionalComponents": [], "requiredComponents": ["毫米波雷达"], "testCycle": 5, "totalComponents": 1}, {"name": "激光雷达标定匹配", "optionalComponents": [], "requiredComponents": ["激光雷达"], "testCycle": 5, "totalComponents": 1}, {"name": "定位IMU模块标定匹配", "optionalComponents": [], "requiredComponents": ["FSD变更"], "testCycle": 7, "totalComponents": 1}, {"name": "感知数采", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 10, "totalComponents": 4}, {"name": "感知开发", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 15, "totalComponents": 6}, {"name": "产线标定", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 5, "totalComponents": 6}, {"name": "底软开发", "optionalComponents": [], "requiredComponents": ["FSD变更"], "testCycle": 20, "totalComponents": 1}, {"name": "数据回传", "optionalComponents": [], "requiredComponents": ["FSD变更", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 8, "totalComponents": 3}, {"name": "地图定位", "optionalComponents": [], "requiredComponents": ["FSD变更"], "testCycle": 12, "totalComponents": 1}, {"name": "行泊主功能开发", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头", "DPB+ESP/IPB", "EPS", "后轮转向", "悬架"], "testCycle": 25, "totalComponents": 10}, {"name": "功能集成测试", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头", "DPB+ESP/IPB", "EPS", "后轮转向", "悬架"], "testCycle": 20, "totalComponents": 10}, {"name": "泛化路试", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头", "DPB+ESP/IPB", "EPS", "后轮转向", "悬架"], "testCycle": 30, "totalComponents": 10}]}, "matchedProjects": [{"name": "行泊主功能开发", "matchedComponents": ["毫米波雷达", "AD摄像头前视/后视/侧视", "DPB+ESP/IPB", "EPS"], "testCycle": 25, "source": "备用数据源", "matchRatio": 0.4, "matchCount": 4, "totalComponents": 10, "complexity": "高", "recommendedVehicles": 6, "aiCalculatedVehicles": 6}, {"name": "功能集成测试", "matchedComponents": ["毫米波雷达", "AD摄像头前视/后视/侧视", "DPB+ESP/IPB", "EPS"], "testCycle": 20, "source": "备用数据源", "matchRatio": 0.4, "matchCount": 4, "totalComponents": 10, "complexity": "高", "recommendedVehicles": 6, "aiCalculatedVehicles": 6}, {"name": "泛化路试", "matchedComponents": ["毫米波雷达", "AD摄像头前视/后视/侧视", "DPB+ESP/IPB", "EPS"], "testCycle": 30, "source": "备用数据源", "matchRatio": 0.4, "matchCount": 4, "totalComponents": 10, "complexity": "高", "recommendedVehicles": 6, "aiCalculatedVehicles": 6}, {"name": "毫米波雷达标定匹配", "matchedComponents": ["毫米波雷达"], "testCycle": 5, "source": "备用数据源", "matchRatio": 0.3333333333333333, "matchCount": 1, "totalComponents": 3, "complexity": "低", "recommendedVehicles": 2, "aiCalculatedVehicles": 2}, {"name": "感知开发", "matchedComponents": ["毫米波雷达", "AD摄像头前视/后视/侧视"], "testCycle": 15, "source": "备用数据源", "matchRatio": 0.3333333333333333, "matchCount": 2, "totalComponents": 6, "complexity": "中", "recommendedVehicles": 4, "aiCalculatedVehicles": 4}, {"name": "产线标定", "matchedComponents": ["毫米波雷达", "AD摄像头前视/后视/侧视"], "testCycle": 5, "source": "备用数据源", "matchRatio": 0.3333333333333333, "matchCount": 2, "totalComponents": 6, "complexity": "低", "recommendedVehicles": 3, "aiCalculatedVehicles": 3}, {"name": "数据回传", "matchedComponents": ["AD摄像头前视/后视/侧视"], "testCycle": 8, "source": "备用数据源", "matchRatio": 0.3333333333333333, "matchCount": 1, "totalComponents": 3, "complexity": "低", "recommendedVehicles": 2, "aiCalculatedVehicles": 2}], "vehicleCalculation": {"totalVehicles": 10, "reasoning": "备用计算：基础10台 × 优先级1.0 = 10台", "projectDetails": [{"name": "行泊主功能开发", "recommendedVehicles": 2, "reasoning": "基于25天测试周期的标准配置"}, {"name": "功能集成测试", "recommendedVehicles": 2, "reasoning": "基于20天测试周期的标准配置"}, {"name": "泛化路试", "recommendedVehicles": 2, "reasoning": "基于30天测试周期的标准配置"}, {"name": "毫米波雷达标定匹配", "recommendedVehicles": 1, "reasoning": "基于5天测试周期的标准配置"}, {"name": "感知开发", "recommendedVehicles": 1, "reasoning": "基于15天测试周期的标准配置"}, {"name": "产线标定", "recommendedVehicles": 1, "reasoning": "基于5天测试周期的标准配置"}, {"name": "数据回传", "recommendedVehicles": 1, "reasoning": "基于8天测试周期的标准配置"}], "riskAssessment": "使用备用计算方法，建议人工复核", "scheduleRecommendations": "建议按测试周期从短到长排序执行", "aiPowered": false, "dataSource": "备用计算算法"}, "schedule": {"startDate": "2025-07-08T00:00:00", "endDate": "2026-06-30T00:00:00", "totalDuration": 25, "projects": [{"name": "行泊主功能开发", "startDate": "2025-07-08T00:00:00", "endDate": "2025-08-02T00:00:00", "duration": 25, "complexity": "高", "vehicles": 6}, {"name": "功能集成测试", "startDate": "2025-07-10T00:00:00", "endDate": "2025-07-30T00:00:00", "duration": 20, "complexity": "高", "vehicles": 6}, {"name": "泛化路试", "startDate": "2025-07-12T00:00:00", "endDate": "2025-08-11T00:00:00", "duration": 30, "complexity": "高", "vehicles": 6}, {"name": "感知开发", "startDate": "2025-07-17T00:00:00", "endDate": "2025-08-01T00:00:00", "duration": 15, "complexity": "中", "vehicles": 4}, {"name": "产线标定", "startDate": "2025-07-20T00:00:00", "endDate": "2025-07-25T00:00:00", "duration": 5, "complexity": "低", "vehicles": 3}, {"name": "毫米波雷达标定匹配", "startDate": "2025-07-23T00:00:00", "endDate": "2025-07-28T00:00:00", "duration": 5, "complexity": "低", "vehicles": 2}, {"name": "数据回传", "startDate": "2025-07-26T00:00:00", "endDate": "2025-08-03T00:00:00", "duration": 8, "complexity": "低", "vehicles": 2}], "aiOptimized": false, "scheduling": "智能算法排期(357天窗口)"}, "summary": {"totalProjects": 7, "totalVehicles": 10, "totalDuration": 25, "riskLevel": "低", "aiPowered": true, "dataSource": "PDF业务逻辑矩阵 + Neo4j知识图谱"}}