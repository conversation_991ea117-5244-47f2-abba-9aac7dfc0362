#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DVP生成API的简化版本
"""

import json
import urllib.request
import time

def test_dvp_generation():
    """测试DVP生成API"""
    print("🧪 测试DVP生成API - 简化版本")
    print("=" * 50)
    
    # API配置
    api_url = "http://localhost:5002/api/generate-dvp"
    headers = {
        "Content-Type": "application/json"
    }
    
    # 测试配置
    test_config = {
        "configType": "ADAS L2+配置",
        "startDate": "2025-07-08",
        "sopDate": "2026-01-08",
        "components": [
            "AD摄像头前视/后视/侧视",
            "毫米波雷达",
            "激光雷达",
            "EPS",
            "DPB+ESP/IPB",
            "环视摄像头"
        ],
        "complexity": "standard",
        "environment": "normal",
        "priority": "normal"
    }
    
    try:
        print(f"📡 发送请求到: {api_url}")
        print(f"📋 测试配置: {test_config['configType']}")
        print(f"📅 时间窗口: {test_config['startDate']} → {test_config['sopDate']}")
        print(f"🔧 组件: {', '.join(test_config['components'])}")
        
        # 发送请求
        data_bytes = json.dumps(test_config).encode('utf-8')
        req = urllib.request.Request(api_url, data_bytes, headers)
        
        start_time = time.time()
        
        # 使用30秒超时，与前端保持一致
        with urllib.request.urlopen(req, timeout=30) as response:
            response_time = time.time() - start_time
            result = json.loads(response.read().decode('utf-8'))
        
        print(f"✅ API 调用成功!")
        print(f"⏱️ 响应时间: {response_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status}")
        
        if result.get('success'):
            data = result.get('data', {})
            summary = data.get('summary', {})
            
            print(f"\n📋 DVP生成结果:")
            print(f"   🚗 所需车辆: {summary.get('totalVehicles', 0)} 台")
            print(f"   ⏰ 测试周期: {summary.get('totalDuration', 0)} 天")
            print(f"   📊 匹配项目: {summary.get('totalProjects', 0)} 个")
            print(f"   📄 报告ID: {data.get('id', 'N/A')}")
            
            # 显示匹配的项目
            matched_projects = data.get('matchedProjects', [])
            if matched_projects:
                print(f"\n🎯 匹配的测试项目:")
                for i, project in enumerate(matched_projects[:5], 1):  # 只显示前5个
                    print(f"   {i}. {project.get('name', 'Unknown')}")
                    print(f"      匹配度: {project.get('matchRatio', 0)*100:.1f}%")
                    print(f"      车辆需求: {project.get('aiCalculatedVehicles', 0)} 台")
            
            print(f"\n✅ DVP生成测试成功!")
            return True
        else:
            print(f"❌ DVP生成失败: {result.get('error', 'Unknown error')}")
            return False
            
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误: {e.code} - {e.reason}")
        try:
            error_response = json.loads(e.read().decode('utf-8'))
            print(f"   错误详情: {error_response.get('error', 'No details')}")
        except:
            pass
        return False
        
    except urllib.error.URLError as e:
        print(f"❌ 连接错误: {e.reason}")
        print("   请确保API服务器正在运行 (python dvp_api_server.py)")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_api_performance():
    """测试API性能"""
    print("\n🚀 测试API性能")
    print("=" * 30)
    
    # 简单的性能测试
    test_urls = [
        ("连接测试", "http://localhost:5002/api/test-connection"),
        ("组件加载", "http://localhost:5002/api/get-components"),
        ("项目加载", "http://localhost:5002/api/get-test-projects")
    ]
    
    for name, url in test_urls:
        try:
            start_time = time.time()
            with urllib.request.urlopen(url, timeout=10) as response:
                response_time = time.time() - start_time
                result = json.loads(response.read().decode('utf-8'))
            
            if result.get('success'):
                print(f"✅ {name}: {response_time:.2f}秒")
            else:
                print(f"⚠️ {name}: {response_time:.2f}秒 (有警告)")
                
        except Exception as e:
            print(f"❌ {name}: 失败 - {str(e)}")

if __name__ == "__main__":
    print("🧪 DVP API 测试套件")
    print("=" * 50)
    
    # 测试API性能
    test_api_performance()
    
    # 测试DVP生成
    success = test_dvp_generation()
    
    if success:
        print(f"\n🎉 所有测试通过!")
        print(f"💡 提示: 现在可以在浏览器中访问 http://localhost:5002 使用界面")
    else:
        print(f"\n⚠️ 测试未完全通过，请检查API服务器状态")
